using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Controllers;

namespace WTO.IdbSubmissions.Web.UnitTests.Controllers;

/// <summary>
/// Unit tests for HomeController
/// </summary>
public class HomeControllerTests
{
    private readonly Mock<ILogger<HomeController>> _mockLogger;
    private readonly HomeController _controller;

    public HomeControllerTests()
    {
        _mockLogger = new Mock<ILogger<HomeController>>();
        _controller = new HomeController(_mockLogger.Object);
    }

    #region Index Action Tests

    [Fact]
    public void Index_WithUnauthenticatedUser_ShouldReturnView()
    {
        // Arrange
        SetupUnauthenticatedUser();

        // Act
        var result = _controller.Index();

        // Assert
        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public void Index_WithAdminUser_ShouldRedirectToAdminsArea()
    {
        // Arrange
        SetupAuthenticatedUser(AdGroupClaims.IdbAdminUser);

        // Act
        var result = _controller.Index();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("Index", redirectResult?.ActionName);
        Assert.Equal("Home", redirectResult?.ControllerName);
        Assert.Equal("Admins", redirectResult?.RouteValues?["area"]);
    }

    [Fact]
    public void Index_WithMemberUser_ShouldRedirectToMembersArea()
    {
        // Arrange
        SetupAuthenticatedUser(AdGroupClaims.IdbMemberUser);

        // Act
        var result = _controller.Index();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("Index", redirectResult?.ActionName);
        Assert.Equal("Home", redirectResult?.ControllerName);
        Assert.Equal("Members", redirectResult?.RouteValues?["area"]);
    }

    [Fact]
    public void Index_WithUserWithoutIdbGroups_ShouldRedirectToAccessDenied()
    {
        // Arrange
        SetupAuthenticatedUser("OTHER_GROUP");

        // Act
        var result = _controller.Index();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("AccessDenied", redirectResult?.ActionName);
        Assert.Equal("Account", redirectResult?.ControllerName);
    }

    [Fact]
    public void Index_WithAdminAndMemberUser_ShouldRedirectToAdminsArea()
    {
        // Arrange - Admin takes precedence over Member
        SetupAuthenticatedUser(AdGroupClaims.IdbAdminUser, AdGroupClaims.IdbMemberUser);

        // Act
        var result = _controller.Index();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("Index", redirectResult?.ActionName);
        Assert.Equal("Home", redirectResult?.ControllerName);
        Assert.Equal("Admins", redirectResult?.RouteValues?["area"]);
    }

    #endregion

    #region Privacy Action Tests

    [Fact]
    public void Privacy_WithUnauthenticatedUser_ShouldReturnView()
    {
        // Arrange
        SetupUnauthenticatedUser();

        // Act
        var result = _controller.Privacy();

        // Assert
        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public void Privacy_WithAdminUser_ShouldRedirectToAdminsArea()
    {
        // Arrange
        SetupAuthenticatedUser(AdGroupClaims.IdbAdminUser);

        // Act
        var result = _controller.Privacy();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("Index", redirectResult?.ActionName);
        Assert.Equal("Home", redirectResult?.ControllerName);
        Assert.Equal("Admins", redirectResult?.RouteValues?["area"]);
    }

    [Fact]
    public void Privacy_WithMemberUser_ShouldRedirectToMembersArea()
    {
        // Arrange
        SetupAuthenticatedUser(AdGroupClaims.IdbMemberUser);

        // Act
        var result = _controller.Privacy();

        // Assert
        Assert.IsType<RedirectToActionResult>(result);
        var redirectResult = result as RedirectToActionResult;
        Assert.Equal("Index", redirectResult?.ActionName);
        Assert.Equal("Home", redirectResult?.ControllerName);
        Assert.Equal("Members", redirectResult?.RouteValues?["area"]);
    }

    #endregion

    #region Error Action Tests

    [Fact]
    public void Error_ShouldReturnViewWithErrorModel()
    {
        // Arrange
        SetupUnauthenticatedUser();

        // Act
        var result = _controller.Error();

        // Assert
        Assert.IsType<ViewResult>(result);
        var viewResult = result as ViewResult;
        Assert.NotNull(viewResult?.Model);
    }

    #endregion

    #region Helper Methods

    private void SetupAuthenticatedUser(params string[] groups)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, "Test User"),
            new(ClaimTypes.NameIdentifier, "123")
        };

        // Add group claims
        foreach (var group in groups)
        {
            claims.Add(new Claim(AdGroupClaims.GroupsClaimType, group));
        }

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);

        var httpContext = new DefaultHttpContext
        {
            User = principal
        };

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };
    }

    private void SetupUnauthenticatedUser()
    {
        var httpContext = new DefaultHttpContext();
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };
    }

    #endregion
}
