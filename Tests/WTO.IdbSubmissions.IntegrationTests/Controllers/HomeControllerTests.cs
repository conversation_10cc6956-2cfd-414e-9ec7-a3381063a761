using System.Net;
using WTO.IdbSubmissions.IntegrationTests.WebApplicationFactory;

namespace WTO.IdbSubmissions.IntegrationTests.Controllers;

/// <summary>
/// Integration tests for HomeController
/// </summary>
public class HomeControllerTests : IntegrationTestBase
{
    public HomeControllerTests(CustomWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task Index_WithUnauthenticatedUser_ShouldReturnHomePage()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("WTO IDB Submissions");
        content.Should().Contain("Clean Architecture");
    }

    [Fact]
    public async Task Index_WithUnauthenticatedUser_ShouldAllowAccess()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Index");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("WTO IDB Submissions");
    }

    [Fact]
    public async Task Privacy_WithUnauthenticatedUser_ShouldReturnPrivacyPage()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Privacy");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Privacy");
    }

    [Fact]
    public async Task Error_ShouldReturnErrorPage()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Error");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Error");
    }

    [Fact]
    public async Task Index_ShouldHaveCorrectContentType()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/");

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("text/html");
    }

    [Fact]
    public async Task Privacy_ShouldHaveCorrectContentType()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Privacy");

        // Assert
        response.Content.Headers.ContentType?.MediaType.Should().Be("text/html");
    }

    [Fact]
    public async Task Index_ShouldNotRequireAuthentication()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        response.StatusCode.Should().NotBe(HttpStatusCode.Forbidden);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task Privacy_ShouldNotRequireAuthentication()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Privacy");

        // Assert
        response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        response.StatusCode.Should().NotBe(HttpStatusCode.Forbidden);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task Index_ShouldRespondQuickly()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var response = await Client.GetAsync("/");

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000); // Should respond within 2 seconds
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task Index_WithConcurrentRequests_ShouldHandleAllRequests()
    {
        // Arrange
        var tasks = new List<Task<HttpResponseMessage>>();
        const int numberOfRequests = 5;

        // Act
        for (int i = 0; i < numberOfRequests; i++)
        {
            tasks.Add(Client.GetAsync("/"));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().HaveCount(numberOfRequests);
        responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.OK);
    }

    [Fact]
    public async Task Index_ShouldContainExpectedElements()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        content.Should().Contain("Clean Architecture");
        content.Should().Contain("Features");
        content.Should().Contain("Project Structure");
        content.Should().Contain("Quick Start");
    }

    [Fact]
    public async Task Privacy_ShouldContainPrivacyInformation()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Home/Privacy");
        var content = await response.Content.ReadAsStringAsync();

        // Assert
        content.Should().Contain("Privacy");
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
