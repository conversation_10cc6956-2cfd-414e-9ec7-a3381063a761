using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Authorization.Constants;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Account controller for authentication-related actions
/// </summary>
public class AccountController : Controller
{
    private readonly ILogger<AccountController> _logger;

    public AccountController(ILogger<AccountController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Initiates the login process by challenging the OpenID Connect authentication
    /// </summary>
    /// <param name="returnUrl">URL to redirect to after successful login</param>
    /// <returns>Challenge result</returns>
    [AllowAnonymous]
    public IActionResult Login(string? returnUrl = null)
    {
        _logger.LogInformation("Login initiated with return URL: {ReturnUrl}", returnUrl ?? "None");
        
        var redirectUrl = Url.Action(nameof(LoginCallback), "Account", new { returnUrl });
        var properties = new AuthenticationProperties { RedirectUri = redirectUrl };
        
        return Challenge(properties, OpenIdConnectDefaults.AuthenticationScheme);
    }

    /// <summary>
    /// Handles the callback from ADFS after authentication
    /// Redirects users to appropriate areas based on their group claims
    /// </summary>
    /// <param name="returnUrl">URL to redirect to after successful login</param>
    /// <returns>Redirect result</returns>
    [AllowAnonymous]
    public IActionResult LoginCallback(string? returnUrl = null)
    {
        _logger.LogInformation("Login callback received for user {UserName} with return URL: {ReturnUrl}",
            User.Identity?.Name ?? "Unknown", returnUrl ?? "None");

        if (!User.Identity?.IsAuthenticated ?? true)
        {
            _logger.LogWarning("Login callback received but user is not authenticated");
            return RedirectToAction("AccessDenied", "Account");
        }

        // If a specific return URL is provided and it's local, use it
        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            _logger.LogInformation("Redirecting authenticated user {UserName} to specified return URL: {ReturnUrl}",
                User.Identity?.Name ?? "Unknown", returnUrl);
            return Redirect(returnUrl);
        }

        // Determine redirect based on user's group claims
        var userGroups = User.Claims
            .Where(c => c.Type == AdGroupClaims.GroupsClaimType)
            .Select(c => c.Value)
            .ToList();

        _logger.LogInformation("User {UserName} has groups: {Groups}",
            User.Identity?.Name ?? "Unknown", string.Join(", ", userGroups));

        // Check for Admin access first (Admins might also have Member access)
        if (userGroups.Contains(AdGroupClaims.IdbAdminUser, StringComparer.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Redirecting admin user {UserName} to Admins area", User.Identity?.Name ?? "Unknown");
            return RedirectToAction("Index", "Home", new { area = "Admins" });
        }

        // Check for Member access
        if (userGroups.Contains(AdGroupClaims.IdbMemberUser, StringComparer.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Redirecting member user {UserName} to Members area", User.Identity?.Name ?? "Unknown");
            return RedirectToAction("Index", "Home", new { area = "Members" });
        }

        // If user doesn't have IDB-specific groups, redirect to access denied
        _logger.LogWarning("User {UserName} does not have IDB Member or Admin groups, redirecting to access denied",
            User.Identity?.Name ?? "Unknown");
        return RedirectToAction("AccessDenied", "Account");
    }

    /// <summary>
    /// Logs out the user from both the local application and Keycloak
    /// </summary>
    /// <returns>Sign out result</returns>
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        var userName = User.Identity?.Name ?? "Unknown";
        _logger.LogInformation("Logout initiated for user {UserName}", userName);

        // Get the id_token from the authentication properties
        var idToken = await HttpContext.GetTokenAsync("id_token");

        // Sign out from the local cookie authentication
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

        // Sign out from Keycloak with id_token_hint
        var properties = new AuthenticationProperties
        {
            RedirectUri = Url.Action("LogoutCallback", "Account")
        };

        // Add the id_token_hint parameter for Keycloak
        if (!string.IsNullOrEmpty(idToken))
        {
            properties.Parameters.Add("id_token_hint", idToken);
        }

        _logger.LogInformation("Signing out user {UserName} from Keycloak", userName);

        return SignOut(properties, OpenIdConnectDefaults.AuthenticationScheme);
    }

    /// <summary>
    /// Handles the callback from ADFS after logout
    /// </summary>
    /// <returns>Redirect to home page</returns>
    [AllowAnonymous]
    public IActionResult LogoutCallback()
    {
        _logger.LogInformation("Logout callback received, redirecting to home page");
        return RedirectToAction("Index", "Home");
    }

    /// <summary>
    /// Access denied page
    /// </summary>
    /// <returns>Access denied view</returns>
    [AllowAnonymous]
    public IActionResult AccessDenied()
    {
        _logger.LogWarning("Access denied page accessed by user {UserName}", User.Identity?.Name ?? "Anonymous");
        return View();
    }
}
