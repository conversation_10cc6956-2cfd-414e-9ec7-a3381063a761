using System.Diagnostics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Models;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Home controller for MVC views - accessible only to unauthenticated users
/// Authenticated users are automatically redirected to their appropriate areas
/// </summary>
[AllowAnonymous]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Home page displaying Clean Architecture information
    /// Redirects authenticated users to their appropriate areas based on role
    /// </summary>
    /// <returns>Index view for unauthenticated users, or redirect for authenticated users</returns>
    public IActionResult Index()
    {
        // If user is authenticated, redirect them to their appropriate area
        if (User.Identity?.IsAuthenticated == true)
        {
            return RedirectToAppropriateArea();
        }

        // Structured logging with Serilog - includes properties for filtering and analysis
        _logger.LogInformation("Home page accessed by unauthenticated user at {Timestamp} from {UserAgent}",
            DateTime.UtcNow,
            Request.Headers.UserAgent.FirstOrDefault() ?? "Unknown");

        var model = new HomeViewModel
        {
            Title = "WTO IDB Submissions - Clean Architecture",
            Description = "A .NET 9 Clean Architecture solution ready for CQRS, MediatR, and Entity Framework Core",
            Features = new List<string>
            {
                "Clean Architecture with proper dependency direction",
                "Domain-driven design with aggregate roots and value objects",
                "Application layer with CQRS-ready structure",
                "Infrastructure layer with repository pattern",
                "Persistence layer ready for Entity Framework Core",
                "Web layer supporting both MVC and API endpoints",
                "Domain events with publisher/subscriber pattern",
                "Unit of Work pattern for transaction management",
                "Base classes and interfaces for rapid development"
            },
            ProjectStructure = new Dictionary<string, string>
            {
                { "Core/Domain", "Business entities, value objects, domain events, and core business rules" },
                { "Core/Application", "Application services, DTOs, interfaces, and use case orchestration" },
                { "Infrastructure/Infrastructure", "External service implementations and cross-cutting concerns" },
                { "Infrastructure/Persistence", "Data access, repository implementations, and database context" },
                { "Presentation/Web", "MVC controllers, API controllers, and user interface" }
            }
        };

        return View(model);
    }

    public IActionResult Privacy()
    {
        // If user is authenticated, redirect them to their appropriate area
        if (User.Identity?.IsAuthenticated == true)
        {
            return RedirectToAppropriateArea();
        }

        _logger.LogInformation("Privacy page accessed by unauthenticated user");
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        var requestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier;
        _logger.LogError("Error page displayed for request {RequestId} by user {UserName}",
            requestId,
            User.Identity?.Name ?? "Anonymous");

        return View(new ErrorViewModel { RequestId = requestId });
    }

    /// <summary>
    /// Redirects authenticated users to their appropriate area based on their group claims
    /// </summary>
    /// <returns>Redirect to appropriate area or access denied</returns>
    private IActionResult RedirectToAppropriateArea()
    {
        // Get user's group claims
        var userGroups = User.Claims
            .Where(c => c.Type == AdGroupClaims.GroupsClaimType)
            .Select(c => c.Value)
            .ToList();

        _logger.LogInformation("Redirecting authenticated user {UserName} with groups: {Groups}",
            User.Identity?.Name ?? "Unknown", string.Join(", ", userGroups));

        // Check for Admin access first (Admins might also have Member access)
        if (userGroups.Contains(AdGroupClaims.IdbAdminUser, StringComparer.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Redirecting admin user {UserName} to Admins area", User.Identity?.Name ?? "Unknown");
            return RedirectToAction("Index", "Home", new { area = "Admins" });
        }

        // Check for Member access
        if (userGroups.Contains(AdGroupClaims.IdbMemberUser, StringComparer.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Redirecting member user {UserName} to Members area", User.Identity?.Name ?? "Unknown");
            return RedirectToAction("Index", "Home", new { area = "Members" });
        }

        // If user doesn't have IDB-specific groups, redirect to access denied
        _logger.LogWarning("User {UserName} does not have IDB Member or Admin groups, redirecting to access denied",
            User.Identity?.Name ?? "Unknown");
        return RedirectToAction("AccessDenied", "Account");
    }
}
