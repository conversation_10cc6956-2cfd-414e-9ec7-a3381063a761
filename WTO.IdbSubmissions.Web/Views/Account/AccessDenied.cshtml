@{
    ViewData["Title"] = "Access Denied";
}

<b-hero type="is-danger" size="is-medium">
    <template #hero-body>
        <div class="container has-text-centered">
            <h1 class="title is-1">403</h1>
            <h2 class="subtitle is-3">
                <span class="has-text-danger">Oops!</span> Access Denied.
            </h2>
            <p class="is-size-5">
                You don't have permission to access this resource.
            </p>
        </div>
    </template>
</b-hero>

<section class="section">
    <div class="container">
        <div class="columns is-centered">
            <div class="column is-half">
                <b-message type="is-warning">
                    <p><strong>This could be because:</strong></p>
                    <div class="content">
                        <ul>
                            <li>You don't have the required group membership</li>
                            <li>Your session has expired</li>
                            <li>You need to contact an administrator for access</li>
                        </ul>
                    </div>
                </b-message>

                <div class="buttons is-centered mt-5">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <b-button type="is-light" tag="a" asp-area="" asp-controller="Account" asp-action="Logout">
                            <b-icon icon="sign-out-alt" size="is-small"></b-icon>
                            <span>Sign Out</span>
                        </b-button>
                    }
                    else
                    {
                        <b-button type="is-primary" tag="a" asp-area="" asp-controller="Home" asp-action="Index">
                            <b-icon icon="home" size="is-small"></b-icon>
                            <span>Go Home</span>
                        </b-button>
                        <b-button type="is-primary" outlined tag="a" asp-area="" asp-controller="Account" asp-action="Login">
                            <b-icon icon="sign-in-alt" size="is-small"></b-icon>
                            <span>Sign In</span>
                        </b-button>
                    }
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Initialize Vue app for this page
        document.addEventListener('DOMContentLoaded', function() {
            if (window.initializeVueApp) {
                window.initializeVueApp({
                    data() {
                        return {
                            // Add any reactive data here if needed
                        }
                    }
                });
            }
        });
    </script>
}
