# ASP.NET Core MVC Areas Setup Documentation

## Overview

This document describes the ASP.NET Core MVC Areas implementation in the WTO IDB Submissions application, including role-based access control and automatic post-login redirection.

## Areas Structure

### 1. Members Area
- **Path**: `/Members/Home/Index`
- **Authorization**: Requires `IdbMemberPolicy` (IDB_MEMBER_USER group claim)
- **Purpose**: Provides access to member-specific functionality for managing IDB submissions
- **Controller**: `WTO.IdbSubmissions.Web.Areas.Members.Controllers.HomeController`
- **Base Class**: Inherits from `IdbMemberController` (which has `[Authorize(Policy = AuthorizationPolicies.IdbMemberPolicy)]`)

### 2. Admins Area
- **Path**: `/Admins/Home/Index`
- **Authorization**: Requires `IdbAdminPolicy` (IDB_ADMIN_USER group claim)
- **Purpose**: Provides access to administrative functionality for managing the IDB system
- **Controller**: `WTO.IdbSubmissions.Web.Areas.Admins.Controllers.HomeController`
- **Base Class**: Inherits from `IdbAdminController` (which has `[Authorize(Policy = AuthorizationPolicies.IdbAdminPolicy)]`)

## Directory Structure

```
WTO.IdbSubmissions.Web/
├── Areas/
│   ├── Members/
│   │   ├── Controllers/
│   │   │   └── HomeController.cs
│   │   └── Views/
│   │       ├── _ViewStart.cshtml
│   │       └── Home/
│   │           └── Index.cshtml
│   └── Admins/
│       ├── Controllers/
│       │   └── HomeController.cs
│       └── Views/
│           ├── _ViewStart.cshtml
│           └── Home/
│               └── Index.cshtml
```

## Routing Configuration

Areas routing is configured in `Program.cs` with the following route mappings:

```csharp
// Area routes must be registered before the default route
app.MapControllerRoute(
    name: "areas",
    pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();
```

## Post-Login Redirection Logic

The `AccountController.LoginCallback` method implements automatic role-based redirection:

1. **Admin Users**: Users with `IDB_ADMIN_USER` group claim are redirected to `/Admins/Home/Index`
2. **Member Users**: Users with `IDB_MEMBER_USER` group claim are redirected to `/Members/Home/Index`
3. **Other Users**: Users without IDB-specific groups are redirected to the main home page
4. **Return URL Priority**: If a specific return URL is provided, it takes precedence over role-based redirection

### Redirection Priority Order:
1. Specific return URL (if provided and local)
2. Admin area (if user has IDB_ADMIN_USER group)
3. Member area (if user has IDB_MEMBER_USER group)
4. Main home page (fallback)

## Navigation Integration

The main layout (`_Layout.cshtml`) includes conditional navigation links based on user permissions:

- **Members Link**: Displayed if user has `IDB_MEMBER_USER` group claim
- **Admins Link**: Displayed if user has `IDB_ADMIN_USER` group claim

## Authorization Implementation

### Base Controllers
- `IdbMemberController`: Base class for Member area controllers with `IdbMemberPolicy` authorization
- `IdbAdminController`: Base class for Admin area controllers with `IdbAdminPolicy` authorization

### Area Controllers
Both area controllers inherit from their respective base controllers, automatically inheriting the authorization requirements:

```csharp
[Area("Members")]
public class HomeController : IdbMemberController
{
    // Automatically protected by IdbMemberPolicy
}

[Area("Admins")]
public class HomeController : IdbAdminController
{
    // Automatically protected by IdbAdminPolicy
}
```

## Testing the Areas

### Manual Testing
1. **Login**: Navigate to `/Account/Login` to authenticate
2. **Automatic Redirection**: After successful login, users are automatically redirected to their appropriate area
3. **Direct Access**: Try accessing areas directly:
   - `/Members/Home/Index` - Should require Member access
   - `/Admins/Home/Index` - Should require Admin access
4. **Navigation**: Use the navigation links in the header to switch between areas

### Expected Behavior
- **Unauthorized Access**: Users without proper permissions receive a 403 Forbidden response and are redirected to `/Account/AccessDenied`
- **Role-Based Access**: Only users with appropriate group claims can access their respective areas
- **Seamless Navigation**: Authenticated users can navigate between areas they have access to

## Future Enhancements

The current implementation provides placeholder pages with basic UI. Future iterations should include:

1. **Members Area**:
   - Submission management functionality
   - Report generation and viewing
   - User profile management

2. **Admins Area**:
   - System configuration
   - User management
   - Data administration
   - System monitoring and reporting

3. **Shared Components**:
   - Common UI components for both areas
   - Shared services and utilities
   - Consistent styling and branding

## Security Considerations

- All area controllers inherit proper authorization from base classes
- Group claims are validated using the existing authorization infrastructure
- Access denied scenarios are handled gracefully with appropriate redirects
- Navigation links are conditionally displayed based on user permissions
- All routes follow the principle of least privilege access
