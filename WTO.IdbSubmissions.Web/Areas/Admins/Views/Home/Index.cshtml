@{
    ViewData["Title"] = "Admins Home";
}

<div class="hero is-danger">
    <div class="hero-body">
        <div class="container">
            <h1 class="title">
                <span class="icon">
                    <i class="fas fa-user-shield"></i>
                </span>
                Welcome to the Admins Area
            </h1>
            <h2 class="subtitle">
                IDB Administration Dashboard
            </h2>
        </div>
    </div>
</div>

<div class="section">
    <div class="container">
        <div class="columns">
            <div class="column">
                <div class="card">
                    <div class="card-content">
                        <div class="media">
                            <div class="media-left">
                                <figure class="image is-48x48">
                                    <span class="icon is-large has-text-warning">
                                        <i class="fas fa-cogs fa-2x"></i>
                                    </span>
                                </figure>
                            </div>
                            <div class="media-content">
                                <p class="title is-4">System Management</p>
                                <p class="subtitle is-6">Manage system settings</p>
                            </div>
                        </div>
                        <div class="content">
                            <p>Configure system-wide settings and parameters.</p>
                            <br>
                            <a class="button is-warning" disabled>
                                <span class="icon">
                                    <i class="fas fa-wrench"></i>
                                </span>
                                <span>System Settings</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column">
                <div class="card">
                    <div class="card-content">
                        <div class="media">
                            <div class="media-left">
                                <figure class="image is-48x48">
                                    <span class="icon is-large has-text-info">
                                        <i class="fas fa-users-cog fa-2x"></i>
                                    </span>
                                </figure>
                            </div>
                            <div class="media-content">
                                <p class="title is-4">User Management</p>
                                <p class="subtitle is-6">Manage user accounts</p>
                            </div>
                        </div>
                        <div class="content">
                            <p>Administer user accounts and permissions.</p>
                            <br>
                            <a class="button is-info" disabled>
                                <span class="icon">
                                    <i class="fas fa-user-plus"></i>
                                </span>
                                <span>Manage Users</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column">
                <div class="card">
                    <div class="card-content">
                        <div class="media">
                            <div class="media-left">
                                <figure class="image is-48x48">
                                    <span class="icon is-large has-text-success">
                                        <i class="fas fa-database fa-2x"></i>
                                    </span>
                                </figure>
                            </div>
                            <div class="media-content">
                                <p class="title is-4">Data Management</p>
                                <p class="subtitle is-6">Manage submissions data</p>
                            </div>
                        </div>
                        <div class="content">
                            <p>Oversee all IDB submissions and data integrity.</p>
                            <br>
                            <a class="button is-success" disabled>
                                <span class="icon">
                                    <i class="fas fa-table"></i>
                                </span>
                                <span>View All Data</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="notification is-danger">
            <p><strong>Welcome, @(User.Identity?.Name ?? "Administrator")!</strong></p>
            <p>You are logged in as an IDB Administrator. This area provides access to administrative functionality for managing the International Database system, users, and all submissions.</p>
            <p><em>Note: This is a placeholder page. Administrative functionality will be implemented in future iterations.</em></p>
        </div>
    </div>
</div>
