using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Controllers;

namespace WTO.IdbSubmissions.Web.Areas.Admins.Controllers;

/// <summary>
/// Home controller for the Admins area
/// Accessible only to users with IDB Admin authorization policy
/// </summary>
[Area("Admins")]
public class HomeController : IdbAdminController
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Admins area home page
    /// </summary>
    /// <returns>Index view for Admins area</returns>
    public IActionResult Index()
    {
        _logger.LogInformation("Admins area home page accessed by user {UserName} at {Timestamp}",
            User.Identity?.Name ?? "Anonymous",
            DateTime.UtcNow);

        return View();
    }
}
