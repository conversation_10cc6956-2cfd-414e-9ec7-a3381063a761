using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Controllers;

namespace WTO.IdbSubmissions.Web.Areas.Members.Controllers;

/// <summary>
/// Home controller for the Members area
/// Accessible only to users with IDB Member authorization policy
/// </summary>
[Area("Members")]
public class HomeController : IdbMemberController
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Members area home page
    /// </summary>
    /// <returns>Index view for Members area</returns>
    public IActionResult Index()
    {
        _logger.LogInformation("Members area home page accessed by user {UserName} at {Timestamp}",
            User.Identity?.Name ?? "Anonymous",
            DateTime.UtcNow);

        return View();
    }
}
